/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent.business
 * 描    述：制度审查智能体服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.business;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.service.agent.core.KnowledgeRetrievalService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService.ChainLlmRequest;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService.ChainStrategy;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService.LlmRequest;
import com.whiskerguard.ai.service.dto.PolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewResponseDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.policy.InternalPolicyReviewService;
import com.whiskerguard.ai.util.LlmResponseProcessor;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 制度审查智能体服务
 * <p>
 * 负责对企业内部制度进行智能审查和优化建议。
 * 通过AI分析制度内容，检查合规性并提供改进建议。
 *
 * 主要功能：
 * 1. 制度内容分析
 * 2. 合规性检查
 * 3. 优化建议生成
 * 4. 风险点识别
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PolicyReviewAgentService {

    private static final Logger log = LoggerFactory.getLogger(PolicyReviewAgentService.class);

    private final KnowledgeRetrievalService knowledgeRetrievalService;
    private final LlmOrchestrationService llmOrchestrationService;
    private final AiInvocationService aiInvocationService;
    private final RetrievalServiceClient retrievalServiceClient;
    private final InternalPolicyReviewService internalPolicyReviewService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public PolicyReviewAgentService(
        KnowledgeRetrievalService knowledgeRetrievalService,
        LlmOrchestrationService llmOrchestrationService,
        AiInvocationService aiInvocationService,
        RetrievalServiceClient retrievalServiceClient,
        InternalPolicyReviewService internalPolicyReviewService
    ) {
        this.knowledgeRetrievalService = knowledgeRetrievalService;
        this.llmOrchestrationService = llmOrchestrationService;
        this.aiInvocationService = aiInvocationService;
        this.retrievalServiceClient = retrievalServiceClient;
        this.internalPolicyReviewService = internalPolicyReviewService;
    }

    /**
     * 审查内部制度
     * <p>
     * 通过Agent方式处理制度审查请求，协调多个步骤的并行和串行执行，提高处理效率
     *
     * @param request 制度审查请求
     * @return 制度审查响应
     */
    public PolicyReviewResponseDTO reviewPolicy(PolicyReviewRequestDTO request) {
        log.info("开始审查内部制度，制度ID: {}, 制度类型: {}", request.getPolicyId(), request.getPolicyType());

        try {
            // 1. 转换为内部制度审查请求DTO (复用PolicyReviewResource中使用的DTO)
            InternalPolicyReviewRequestDTO internalRequest = convertToInternalRequest(request);

            // 2. 并行执行检索任务
            CompletableFuture<String> regulationStandardsFuture = CompletableFuture.supplyAsync(() -> retrieveRegulationStandards(request));
            CompletableFuture<String> industryBestPracticesFuture = CompletableFuture.supplyAsync(() -> retrieveIndustryBestPractices(request));
            CompletableFuture<String> historicalPoliciesFuture = CompletableFuture.supplyAsync(() -> retrieveHistoricalPolicies(request));

            // 3. 等待检索任务完成
            CompletableFuture<Void> allRetrievals = CompletableFuture.allOf(
                regulationStandardsFuture,
                industryBestPracticesFuture,
                historicalPoliciesFuture
            );

            // 等待所有检索任务完成
            allRetrievals.join();

            // 4. 获取检索结果
            String regulationStandards = regulationStandardsFuture.get();
            String industryBestPractices = industryBestPracticesFuture.get();
            String historicalPolicies = historicalPoliciesFuture.get();

            // 5. 将检索结果添加到请求中
            internalRequest.getMetadata().put("regulationStandards", regulationStandards);
            internalRequest.getMetadata().put("industryBestPractices", industryBestPractices);
            internalRequest.getMetadata().put("historicalPolicies", historicalPolicies);

            // 6. 利用已有的InternalPolicyReviewService处理请求
            log.debug("调用内部制度审查服务执行审查");
            InternalPolicyReviewResponseDTO internalResponse = internalPolicyReviewService.reviewInternalPolicy(internalRequest);

            // 7. 转换为Agent响应格式并返回
            return convertToAgentResponse(internalResponse, request);
        } catch (Exception e) {
            log.error("审查内部制度失败", e);
            throw new RuntimeException("制度审查处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将Agent请求转换为内部制度审查请求
     */
    private InternalPolicyReviewRequestDTO convertToInternalRequest(PolicyReviewRequestDTO request) {
        InternalPolicyReviewRequestDTO internalRequest = new InternalPolicyReviewRequestDTO();

        // 复制基本字段
        internalRequest.setTenantId(request.getTenantId());
        internalRequest.setEmployeeId(request.getEmployeeId());
        internalRequest.setPolicyContent(request.getPolicyContent());
        internalRequest.setPolicyType(request.getPolicyType());
        internalRequest.setPolicyTitle(request.getPolicyTitle());
        internalRequest.setCompanyId(request.getCompanyId());
        internalRequest.setCompanyName(request.getCompanyName());
        internalRequest.setIndustry(request.getIndustryType());

        // 处理优先级
        if (request.getPriority() != null) {
            // 将字符串优先级转换为枚举或直接设置字符串
            try {
                InternalPolicyReviewRequestDTO.ReviewPriority priority =
                    InternalPolicyReviewRequestDTO.ReviewPriority.valueOf(request.getPriority());
                internalRequest.setReviewPriority(priority);
                internalRequest.setPriority(priority.name());
            } catch (IllegalArgumentException e) {
                // 如果无法转换为枚举，则直接设置字符串
                internalRequest.setPriority(request.getPriority());
            }
        } else {
            internalRequest.setPriority("NORMAL");
        }

        // 设置制定部门
        internalRequest.setDepartment(
            request.getDepartment() != null ? request.getDepartment() : "未指定部门"
        );

        // 初始化元数据
        Map<String, Object> metadata = new HashMap<>();

        // 如果请求有指定的AI模型，添加到元数据
        if (request.getModelNames() != null && !request.getModelNames().isEmpty()) {
            metadata.put("aiModel", request.getModelNames().get(0));
        }

        // 添加审查重点
        metadata.put("reviewFocus", request.getReviewFocus());

        // 添加Agent标记，便于跟踪来源
        metadata.put("sourceType", "AGENT");
        metadata.put("agentTaskId", request.getAgentTaskId());

        internalRequest.setMetadata(metadata);

        // 生成唯一请求ID
        internalRequest.setRequestId(UUID.randomUUID().toString());

        return internalRequest;
    }

    /**
     * 将内部响应转换为Agent响应
     */
    private PolicyReviewResponseDTO convertToAgentResponse(InternalPolicyReviewResponseDTO internalResponse, PolicyReviewRequestDTO request) {
        return PolicyReviewResponseDTO.builder()
            .policyId(request.getPolicyId())
            .companyId(request.getCompanyId())
            .reviewResult(internalResponse.getReviewSummary())
            .complianceScore(internalResponse.getRiskScore())
            .riskLevel(internalResponse.getOverallRiskLevel() != null ? internalResponse.getOverallRiskLevel().toString() : "UNKNOWN")
            .issues(internalResponse.getRiskPoints())
            .recommendations(internalResponse.getOptimizationSuggestions())
            .compliancePoints(formatCompliancePoints(internalResponse))
            .status("COMPLETED")
            .reviewedAt(Instant.now())
            .build();
    }

    /**
     * 格式化合规点列表
     * 将内部响应中的风险点转换为字符串列表
     */
    private List<String> formatCompliancePoints(PolicyReviewInternalResponseDTO internalResponse) {
        // 将PolicyRiskPointDTO列表转换为String列表
        if (internalResponse.getRiskPoints() != null) {
            return internalResponse.getRiskPoints().stream()
                .map(point -> point.getDescription())
                .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 检索相关法规标准
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveRegulationStandards(PolicyReviewRequestDTO request) {
        log.debug("检索相关法规标准，制度类型: {}", request.getPolicyType());

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("policyType", request.getPolicyType());
        queryParams.put("industryType", request.getIndustryType());

        // 如果提供了制度ID，添加到查询参数
        if (request.getPolicyId() != null) {
            queryParams.put("relatedPolicyId", request.getPolicyId());
        }

        // 构建唯一的缓存键
        String cacheKey = "policy_regulation_standards_" + request.getPolicyType();

        // 利用KnowledgeRetrievalService进行检索，自动处理缓存
        return knowledgeRetrievalService.retrieveKnowledge(
            request.getTenantId(),
            "REGULATION",
            cacheKey,
            queryParams
        );
    }

    /**
     * 检索行业最佳实践
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveIndustryBestPractices(PolicyReviewRequestDTO request) {
        log.debug("检索行业最佳实践，行业类型: {}", request.getIndustryType());

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("industryType", request.getIndustryType());
        queryParams.put("policyType", request.getPolicyType());

        // 构建唯一的缓存键
        String cacheKey = "policy_industry_practice_" + request.getIndustryType() + "_" + request.getPolicyType();

        // 利用KnowledgeRetrievalService进行检索，自动处理缓存
        return knowledgeRetrievalService.retrieveKnowledge(
            request.getTenantId(),
            "INDUSTRY_PRACTICE",
            cacheKey,
            queryParams
        );
    }

    /**
     * 检索企业历史制度
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveHistoricalPolicies(PolicyReviewRequestDTO request) {
        log.debug("检索企业历史制度，企业ID: {}", request.getCompanyId());

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("companyId", request.getCompanyId());
        queryParams.put("policyType", request.getPolicyType());

        // 排除当前审查的制度
        if (request.getPolicyId() != null) {
            queryParams.put("excludePolicyId", request.getPolicyId());
        }

        // 构建唯一的缓存键
        String cacheKey = "historical_policies_" + request.getCompanyId() + "_" + request.getPolicyType();

        // 利用KnowledgeRetrievalService进行检索，自动处理缓存
        return knowledgeRetrievalService.retrieveKnowledge(
            request.getTenantId(),
            "COMPANY_POLICY",
            cacheKey,
            queryParams
        );
    }

    /**
     * 以下方法保留用于直接使用LLM进行分析的场景，
     * 当InternalPolicyReviewService不可用时可以作为备选方案
     */

    /**
     * 分析制度内容结构
     */
    private String analyzeContentStructure(PolicyReviewRequestDTO request) {
        log.debug("分析制度内容结构");

        String prompt = buildContentAnalysisPrompt(request);

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "policy_content_analysis",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            3
        );
    }

    /**
     * 检查合规性
     */
    private String checkCompliance(PolicyReviewRequestDTO request, String regulationStandards) {
        log.debug("检查制度合规性");

        String prompt = buildComplianceCheckPrompt(request, regulationStandards);

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "policy_compliance_check",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            3
        );
    }

    /**
     * 识别风险点
     */
    private String identifyRisks(PolicyReviewRequestDTO request, String regulationStandards) {
        log.debug("识别制度风险点");

        String prompt = buildRiskIdentificationPrompt(request, regulationStandards);

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "policy_risk_identification",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            3
        );
    }

    /**
     * 生成优化建议
     */
    private String generateOptimizationSuggestions(
        PolicyReviewRequestDTO request,
        String contentAnalysis,
        String complianceCheck,
        String riskIdentification,
        String industryBestPractices
    ) {
        log.debug("生成制度优化建议");

        String prompt = buildOptimizationPrompt(request, contentAnalysis, complianceCheck, riskIdentification, industryBestPractices);

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "policy_optimization",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            3
        );
    }

    /**
     * 备选方案：并行执行多个分析任务并整合结果
     */
    private PolicyReviewResponseDTO parallelReviewPolicy(PolicyReviewRequestDTO request, String regulationStandards, String industryBestPractices) {
        log.debug("并行执行多个分析任务");

        // 创建任务列表
        List<LlmRequest> parallelTasks = new ArrayList<>();

        // 添加内容分析任务
        parallelTasks.add(new LlmRequest(
            "policy_content_analysis",
            buildContentAnalysisPrompt(request),
            Map.of("task", "content_analysis")
        ));

        // 添加合规检查任务
        parallelTasks.add(new LlmRequest(
            "policy_compliance_check",
            buildComplianceCheckPrompt(request, regulationStandards),
            Map.of("task", "compliance_check")
        ));

        // 添加风险识别任务
        parallelTasks.add(new LlmRequest(
            "policy_risk_identification",
            buildRiskIdentificationPrompt(request, regulationStandards),
            Map.of("task", "risk_identification")
        ));

        // 并行执行任务，等待所有结果
        List<String> results = llmOrchestrationService.batchInvokeLlm(request.getTenantId(), parallelTasks);

        // 整合结果
        String contentAnalysis = results.get(0);
        String complianceCheck = results.get(1);
        String riskIdentification = results.get(2);

        // 根据前面的分析结果，生成优化建议
        String optimizationSuggestions = generateOptimizationSuggestions(
            request, contentAnalysis, complianceCheck, riskIdentification, industryBestPractices
        );

        // 构建响应
        return buildPolicyReviewResponse(
            request, contentAnalysis, complianceCheck, riskIdentification, optimizationSuggestions
        );
    }

    /**
     * 构建内容分析提示词
     */
    private String buildContentAnalysisPrompt(PolicyReviewRequestDTO request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下内部制度进行内容结构分析：\n\n");
        prompt.append("【制度内容】\n").append(request.getPolicyContent()).append("\n\n");
        prompt.append("【制度类型】\n").append(request.getPolicyType()).append("\n\n");

        prompt.append("请从以下方面进行分析：\n");
        prompt.append("1. 制度结构完整性（目的、适用范围、职责分工、具体规定、监督检查等）\n");
        prompt.append("2. 内容逻辑性和条理性\n");
        prompt.append("3. 条款表述的准确性和明确性\n");
        prompt.append("4. 可操作性和实用性\n");
        prompt.append("5. 制度的系统性和协调性\n");

        return prompt.toString();
    }

    /**
     * 构建合规性检查提示词
     */
    private String buildComplianceCheckPrompt(PolicyReviewRequestDTO request, String regulationStandards) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请检查以下内部制度的合规性：\n\n");
        prompt.append("【制度内容】\n").append(request.getPolicyContent()).append("\n\n");
        prompt.append("【相关法规标准】\n").append(regulationStandards).append("\n\n");

        prompt.append("请从以下方面进行检查：\n");
        prompt.append("1. 制度条款是否符合法律法规要求\n");
        prompt.append("2. 制度内容是否与上级政策冲突\n");
        prompt.append("3. 制度执行是否满足合规标准\n");
        prompt.append("4. 制度监督机制是否健全\n");
        prompt.append("5. 违规处理措施是否合理\n");

        return prompt.toString();
    }

    /**
     * 构建风险识别提示词
     */
    private String buildRiskIdentificationPrompt(PolicyReviewRequestDTO request, String regulationStandards) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请识别以下内部制度的潜在风险点：\n\n");
        prompt.append("【制度内容】\n").append(request.getPolicyContent()).append("\n\n");
        prompt.append("【相关法规标准】\n").append(regulationStandards).append("\n\n");

        prompt.append("请识别以下类型的风险：\n");
        prompt.append("1. 合规风险（违反法律法规的风险）\n");
        prompt.append("2. 操作风险（执行过程中的风险）\n");
        prompt.append("3. 管理风险（管理漏洞和盲区）\n");
        prompt.append("4. 责任风险（责任不清或推诿的风险）\n");
        prompt.append("5. 监督风险（监督不到位的风险）\n");

        return prompt.toString();
    }

    /**
     * 构建优化建议提示词
     */
    private String buildOptimizationPrompt(
        PolicyReviewRequestDTO request,
        String contentAnalysis,
        String complianceCheck,
        String riskIdentification,
        String industryBestPractices
    ) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请基于以下分析结果，为内部制度提供优化建议：\n\n");
        prompt.append("【制度内容】\n").append(request.getPolicyContent()).append("\n\n");
        prompt.append("【内容分析】\n").append(contentAnalysis).append("\n\n");
        prompt.append("【合规性检查】\n").append(complianceCheck).append("\n\n");
        prompt.append("【风险识别】\n").append(riskIdentification).append("\n\n");
        prompt.append("【行业最佳实践】\n").append(industryBestPractices).append("\n\n");

        prompt.append("请提供以下方面的优化建议：\n");
        prompt.append("1. 结构优化建议（如何完善制度结构）\n");
        prompt.append("2. 内容优化建议（如何改进具体条款）\n");
        prompt.append("3. 合规性改进建议（如何确保合规）\n");
        prompt.append("4. 风险防控建议（如何降低风险）\n");
        prompt.append("5. 执行效果提升建议（如何提高可操作性）\n");

        return prompt.toString();
    }

    /**
     * 构建制度审查响应
     */
    private PolicyReviewResponseDTO buildPolicyReviewResponse(
        PolicyReviewRequestDTO request,
        String contentAnalysis,
        String complianceCheck,
        String riskIdentification,
        String optimizationSuggestions
    ) {
        // 从分析结果中提取风险等级和评分
        String riskLevel = extractRiskLevel(complianceCheck, riskIdentification);
        int complianceScore = extractComplianceScore(complianceCheck);

        // 从风险识别中提取风险点列表
        List<String> issues = extractIssues(riskIdentification);

        // 从优化建议中提取建议列表
        List<String> recommendations = extractRecommendations(optimizationSuggestions);

        return PolicyReviewResponseDTO.builder()
            .policyId(request.getPolicyId())
            .companyId(request.getCompanyId())
            .reviewResult(contentAnalysis)
            .complianceScore(complianceScore)
            .riskLevel(riskLevel)
            .issues(issues)
            .recommendations(recommendations)
            .compliancePoints(complianceCheck)
            .status("COMPLETED")
            .reviewedAt(Instant.now())
            .build();
    }

    /**
     * 从合规性检查中提取合规评分
     */
    private int extractComplianceScore(String complianceCheck) {
        // 默认评分
        int defaultScore = 75;

        try {
            // 寻找包含"评分"、"分数"、"得分"等关键词的行
            String[] lines = complianceCheck.split("\n");
            for (String line : lines) {
                if (line.contains("评分") || line.contains("分数") || line.contains("得分")) {
                    // 提取数字
                    String[] parts = line.replaceAll("[^0-9]", " ").trim().split("\\s+");
                    if (parts.length > 0 && StringUtils.hasText(parts[0])) {
                        int score = Integer.parseInt(parts[0]);
                        if (score >= 0 && score <= 100) {
                            return score;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取合规评分失败，使用默认值", e);
        }

        return defaultScore;
    }

    /**
     * 从风险识别中提取风险等级
     */
    private String extractRiskLevel(String complianceCheck, String riskIdentification) {
        // 默认风险等级
        String defaultLevel = "MEDIUM";

        try {
            // 检查是否包含高风险关键词
            if (riskIdentification.contains("高风险") || riskIdentification.contains("严重风险") ||
                riskIdentification.contains("重大风险") || complianceCheck.contains("合规性较差")) {
                return "HIGH";
            }

            // 检查是否包含低风险关键词
            if ((riskIdentification.contains("低风险") || riskIdentification.contains("风险较小")) &&
                !riskIdentification.contains("高风险") && !riskIdentification.contains("中等风险") &&
                (complianceCheck.contains("合规性良好") || complianceCheck.contains("基本符合"))) {
                return "LOW";
            }
        } catch (Exception e) {
            log.warn("提取风险等级失败，使用默认值", e);
        }

        return defaultLevel;
    }

    /**
     * 从风险识别中提取风险点列表
     */
    private List<String> extractIssues(String riskIdentification) {
        List<String> issues = new ArrayList<>();

        try {
            String[] lines = riskIdentification.split("\n");
            for (String line : lines) {
                // 寻找数字编号开头的行，通常表示列表项
                if (line.matches("^\\s*\\d+\\..*") || line.matches("^\\s*•.*") || line.matches("^\\s*-.*")) {
                    // 清理行，去除编号和多余空格
                    String issue = line.replaceFirst("^\\s*\\d+\\.\\s*|^\\s*•\\s*|^\\s*-\\s*", "").trim();
                    if (StringUtils.hasText(issue)) {
                        issues.add(issue);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取风险点失败", e);
        }

        // 如果没有提取到风险点，添加一个默认项
        if (issues.isEmpty()) {
            issues.add("未能明确识别风险点，建议进行人工审核");
        }

        return issues;
    }

    /**
     * 从优化建议中提取建议列表
     */
    private List<String> extractRecommendations(String optimizationSuggestions) {
        List<String> recommendations = new ArrayList<>();

        try {
            String[] lines = optimizationSuggestions.split("\n");
            for (String line : lines) {
                // 寻找数字编号开头的行，通常表示列表项
                if (line.matches("^\\s*\\d+\\..*") || line.matches("^\\s*•.*") || line.matches("^\\s*-.*")) {
                    // 清理行，去除编号和多余空格
                    String recommendation = line.replaceFirst("^\\s*\\d+\\.\\s*|^\\s*•\\s*|^\\s*-\\s*", "").trim();
                    if (StringUtils.hasText(recommendation)) {
                        recommendations.add(recommendation);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取建议失败", e);
        }

        // 如果没有提取到建议，添加一个默认项
        if (recommendations.isEmpty()) {
            recommendations.add("建议进行制度结构优化，确保包含目的、适用范围、职责分工、具体规定和监督检查等必要内容");
            recommendations.add("提高条款表述的准确性和可操作性");
        }

        return recommendations;
    }
}
